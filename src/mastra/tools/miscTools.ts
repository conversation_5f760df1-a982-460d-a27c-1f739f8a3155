import { MCPClient } from "@mastra/mcp";

export const riotMCP = new MCPClient({
    servers: {
        riot: {
            command: "npx",
            args: [
                "-y",
                "@smithery/cli@latest",
                "run",
                "@jifrozen0110/riot",
                "--key",
                "e350793c-8337-47e3-8da1-f45ed9d164ce",
                "--profile",
                "ripe-felidae-lOKJL8",
            ],
        },
    },
});

const toolsCollection = await riotMCP.getTools();

export const miscTools = toolsCollection;

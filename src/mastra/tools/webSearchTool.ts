import { MCPClient } from "@mastra/mcp";

export const webSearchMCP = new MCPClient({
    servers: {
        web_search: {
            command: "npx",
            args: ["-y", "exa-mcp-server", "--tools=web_search_exa"],
            env: {
                EXA_API_KEY: "4e01cf2d-7b82-4ee3-ad97-21b7fd805e78",
            },
        },
    },
});

const toolsCollection = await webSearchMCP.getTools();

// console.log({ web_search_web_search_exa: toolsCollection?.web_search_web_search_exa });

export const webSearchTool = toolsCollection;
